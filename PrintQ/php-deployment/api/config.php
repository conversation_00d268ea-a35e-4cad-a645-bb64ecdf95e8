<?php
// PrintQ PHP Configuration
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: https://printq.cloud');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');

// Cache busting headers
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');
header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Data directory configuration
define('DATA_DIR', __DIR__ . '/../data/');
define('JOBS_FILE', DATA_DIR . 'jobs.json');
define('PRINTERS_FILE', DATA_DIR . 'printers.json');
define('SETTINGS_FILE', DATA_DIR . 'settings.json');

// Ensure data directory exists
if (!is_dir(DATA_DIR)) {
    mkdir(DATA_DIR, 0755, true);
}

// Utility functions
function readJsonFile($filename) {
    if (!file_exists($filename)) {
        return [];
    }
    $content = file_get_contents($filename);
    return json_decode($content, true) ?: [];
}

function writeJsonFile($filename, $data) {
    // Ensure directory exists
    $dir = dirname($filename);
    if (!is_dir($dir)) {
        if (!mkdir($dir, 0755, true)) {
            error_log("Failed to create directory: $dir");
            return false;
        }
    }

    // Check if directory is writable
    if (!is_writable($dir)) {
        error_log("Directory not writable: $dir");
        return false;
    }

    $json = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    if ($json === false) {
        error_log("JSON encoding failed: " . json_last_error_msg());
        return false;
    }

    $result = file_put_contents($filename, $json, LOCK_EX);
    if ($result === false) {
        error_log("Failed to write file: $filename");
    }

    return $result;
}

function sendJsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data);
    exit();
}

function sendErrorResponse($message, $statusCode = 400) {
    http_response_code($statusCode);
    echo json_encode(['error' => $message]);
    exit();
}

function getRequestBody() {
    $input = file_get_contents('php://input');

    error_log("getRequestBody: Raw input length: " . strlen($input));
    error_log("getRequestBody: Content-Type: " . ($_SERVER['CONTENT_TYPE'] ?? 'not set'));
    error_log("getRequestBody: Request method: " . $_SERVER['REQUEST_METHOD']);

    if (empty($input)) {
        error_log("getRequestBody: Empty request body received");
        return null;
    }

    $data = json_decode($input, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("getRequestBody: JSON decode error: " . json_last_error_msg() . " - Input: " . substr($input, 0, 500));
        return null;
    }

    error_log("getRequestBody: Successfully decoded JSON with " . count($data) . " keys");
    return $data;
}

// Initialize empty data files if they don't exist
function initializeDataFiles() {
    // Initialize jobs with empty array
    if (!file_exists(JOBS_FILE)) {
        writeJsonFile(JOBS_FILE, []);
    }

    // Initialize printers with empty array
    if (!file_exists(PRINTERS_FILE)) {
        writeJsonFile(PRINTERS_FILE, []);
    }
    
    // Initialize settings
    if (!file_exists(SETTINGS_FILE)) {
        $sampleSettings = [
            'colors' => [
                ['value' => '#3B82F6', 'label' => 'Blue'],
                ['value' => '#EF4444', 'label' => 'Red'],
                ['value' => '#10B981', 'label' => 'Green'],
                ['value' => '#F59E0B', 'label' => 'Yellow'],
                ['value' => '#8B5CF6', 'label' => 'Purple'],
                ['value' => '#F97316', 'label' => 'Orange'],
                ['value' => '#6B7280', 'label' => 'Gray'],
                ['value' => '#000000', 'label' => 'Black'],
                ['value' => '#FFFFFF', 'label' => 'White']
            ],
            'createdAt' => date('c'),
            'updatedAt' => date('c')
        ];
        writeJsonFile(SETTINGS_FILE, $sampleSettings);
    }
}

// Initialize data files
initializeDataFiles();
?>
