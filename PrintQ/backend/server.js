import express from 'express'
import cors from 'cors'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'
import jobRoutes from './routes/jobs.js'
import printerRoutes from './routes/printers.js'
import partRoutes from './routes/parts.js'
import settingsRoutes from './routes/settings.js'
import { initializeSampleData } from './utils/dataStore.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const app = express()
const PORT = process.env.PORT || 3001

// CORS configuration
const corsOptions = {
  origin: process.env.NODE_ENV === 'production'
    ? ['https://printq.cloud', 'https://www.printq.cloud']
    : ['http://localhost:5173', 'http://localhost:3000', 'http://127.0.0.1:5173'],
  credentials: true,
  optionsSuccessStatus: 200
}

// Middleware
app.use(cors(corsOptions))
app.use(express.json())

// Cache busting middleware for API routes
app.use('/api', (req, res, next) => {
  // Set cache-busting headers for all API responses
  res.set({
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0',
    'Last-Modified': new Date().toUTCString()
  })
  next()
})

// Serve static files from frontend build (only in production)
if (process.env.NODE_ENV === 'production') {
  const frontendPath = join(__dirname, '../frontend')
  app.use(express.static(frontendPath))
}

// Routes
app.use('/api/jobs', jobRoutes)
app.use('/api/printers', printerRoutes)
app.use('/api/parts', partRoutes)
app.use('/api/settings', settingsRoutes)

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() })
})

// Serve React app for all non-API routes (SPA fallback) - only in production
if (process.env.NODE_ENV === 'production') {
  app.get('*', (req, res) => {
    // Don't serve index.html for API routes
    if (req.path.startsWith('/api/')) {
      return res.status(404).json({ error: 'API route not found' })
    }
    const frontendPath = join(__dirname, '../frontend')
    res.sendFile(join(frontendPath, 'index.html'))
  })
} else {
  // 404 handler for development
  app.use('*', (req, res) => {
    res.status(404).json({ error: 'Route not found' })
  })
}

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack)
  res.status(500).json({ error: 'Something went wrong!' })
})

app.listen(PORT, async () => {
  console.log(`PrintQ Backend server running on port ${PORT}`)

  if (process.env.NODE_ENV === 'production') {
    console.log(`🌐 Application: https://printq.cloud`)
    console.log(`🔍 Health check: https://printq.cloud/api/health`)
    console.log(`📊 Environment: Production`)
  } else {
    console.log(`🔍 Health check: http://localhost:${PORT}/api/health`)
    console.log(`📊 Environment: Development`)
  }

  // Initialize sample data
  await initializeSampleData()
  console.log(`✅ Sample data initialized`)
})
