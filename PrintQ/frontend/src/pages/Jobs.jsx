import { useState, useEffect } from 'react'
import { Link, useNavigate, useSearchParams } from 'react-router-dom'
import { Search, Filter, Calendar, User, Package, CheckCircle, Circle, Plus } from 'lucide-react'
import api from '../utils/api'

const Jobs = () => {
  const navigate = useNavigate()
  const [searchParams, setSearchParams] = useSearchParams()
  const [jobs, setJobs] = useState([])
  const [filteredJobs, setFilteredJobs] = useState([])
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('uncompleted')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchJobs()
  }, [])

  useEffect(() => {
    filterJobs()
  }, [jobs, searchTerm, statusFilter])

  // Check for refresh parameter and refetch data if needed
  useEffect(() => {
    const shouldRefresh = searchParams.get('refresh')
    if (shouldRefresh === 'true') {
      fetchJobs()
      // Remove the refresh parameter from URL
      setSearchParams({})
    }
  }, [searchParams, setSearchParams])



  const fetchJobs = async () => {
    try {
      const jobsData = await api.jobs.getAll()
      setJobs(jobsData)
      setLoading(false)
    } catch (error) {
      console.error('Error fetching jobs:', error)
      setLoading(false)
    }
  }

  const handleToggleCompletion = async (jobId, event) => {
    // Prevent the Link navigation when clicking the button
    event.preventDefault()
    event.stopPropagation()

    const job = jobs.find(j => j.id === jobId)
    const isCompleted = job.status === 'completed'
    const action = isCompleted ? 'mark as incomplete' : 'mark as completed'

    if (window.confirm(`Are you sure you want to ${action} this job?`)) {
      try {
        const updates = {
          status: isCompleted ? 'queued' : 'completed',
          updatedAt: new Date().toISOString()
        }

        // Add or remove completedAt timestamp
        if (isCompleted) {
          updates.completedAt = null
        } else {
          updates.completedAt = new Date().toISOString()
        }

        await api.jobs.update(jobId, updates)

        // Update local state
        setJobs(prev => prev.map(job =>
          job.id === jobId
            ? { ...job, ...updates }
            : job
        ))
      } catch (error) {
        console.error('Error updating job status:', error)
        alert('Failed to update job status. Please try again.')
      }
    }
  }

  const filterJobs = () => {
    let filtered = jobs

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(job =>
        job.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        job.clientName.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Filter by status
    if (statusFilter !== 'all') {
      if (statusFilter === 'uncompleted') {
        filtered = filtered.filter(job => job.status !== 'completed')
      } else {
        filtered = filtered.filter(job => job.status === statusFilter)
      }
    }

    setFilteredJobs(filtered)
  }



  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString()
  }

  const getTotalParts = (parts) => {
    return parts.reduce((total, part) => total + part.quantity, 0)
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Print Jobs</h1>
        <p className="text-gray-600">Manage and track your 3D printing jobs</p>
      </div>

      {/* Filters */}
      <div className="card p-4">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search jobs or clients..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 input-field"
              />
            </div>
          </div>

          {/* Status Filter */}
          <div className="md:w-48">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="input-field"
            >
              <option value="all">All Status</option>
              <option value="uncompleted">Uncompleted</option>
              <option value="completed">Completed</option>
            </select>
          </div>
        </div>
      </div>

      {/* Jobs Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredJobs.map((job) => (
          <div key={job.id} className="card p-6 hover:shadow-lg transition-shadow duration-200">
            <div className="space-y-4">
              {/* Header */}
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <Link to={`/jobs/${job.id}`}>
                    <h3 className="text-lg font-semibold text-gray-900 hover:text-primary-600 transition-colors">{job.name}</h3>
                  </Link>
                  <div className="flex items-center space-x-1 text-sm text-gray-600">
                    <User className="h-4 w-4" />
                    <span>{job.clientName}</span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={(e) => handleToggleCompletion(job.id, e)}
                    className={`transition-colors ${
                      job.status === 'completed'
                        ? 'text-success-600 hover:text-gray-400'
                        : 'text-gray-400 hover:text-success-600'
                    }`}
                    title={job.status === 'completed' ? 'Mark as incomplete' : 'Mark as completed'}
                  >
                    {job.status === 'completed' ? (
                      <CheckCircle className="h-4 w-4" />
                    ) : (
                      <Circle className="h-4 w-4" />
                    )}
                  </button>
                </div>
              </div>

              <Link to={`/jobs/${job.id}`} className="block">
                {/* Parts Summary */}
                <div className="flex items-center space-x-1 text-sm text-gray-600">
                  <Package className="h-4 w-4" />
                  <span>{job.parts.length} part types, {getTotalParts(job.parts)} total pieces</span>
                </div>

                {/* Dates */}
                <div className="space-y-1 text-sm text-gray-600">
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-4 w-4" />
                    <span>Created: {formatDate(job.createdAt)}</span>
                  </div>
                  {job.dueDate && (
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>Due: {formatDate(job.dueDate)}</span>
                    </div>
                  )}
                </div>

                {/* Parts Preview */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-700">Parts:</h4>
                  <div className="space-y-1">
                    {job.parts.slice(0, 2).map((part) => (
                      <div key={part.id} className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">{part.name}</span>
                        <div className="flex items-center space-x-2">
                          <span className="text-gray-500">×{part.quantity}</span>
                          <div className="flex space-x-1">
                            {(part.colors || [part.color]).slice(0, 2).map((color, index) => (
                              <div
                                key={index}
                                className="w-3 h-3 rounded-full border border-gray-300"
                                style={{ backgroundColor: color }}
                              />
                            ))}
                            {(part.colors || [part.color]).length > 2 && (
                              <div className="w-3 h-3 rounded-full border border-gray-300 bg-gray-100 flex items-center justify-center">
                                <span className="text-xs text-gray-600">+</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                    {job.parts.length > 2 && (
                      <div className="text-xs text-gray-500">
                        +{job.parts.length - 2} more parts
                      </div>
                    )}
                  </div>
                </div>
              </Link>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {filteredJobs.length === 0 && (
        <div className="text-center py-16">
          <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-6">
            <Package className="h-12 w-12 text-gray-400" />
          </div>

          {searchTerm || statusFilter !== 'all' ? (
            // Filtered empty state
            <>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No jobs match your criteria</h3>
              <p className="text-gray-500 mb-6 max-w-md mx-auto">
                Try adjusting your search terms or filter settings to find the jobs you're looking for.
              </p>
              <button
                onClick={() => {
                  setSearchTerm('')
                  setStatusFilter('uncompleted')
                }}
                className="btn-secondary"
              >
                Clear Filters
              </button>
            </>
          ) : jobs.length === 0 ? (
            // Truly empty state (no jobs at all)
            <>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Welcome to PrintQ!</h3>
              <p className="text-gray-500 mb-8 max-w-md mx-auto">
                You haven't created any print jobs yet. Get started by creating your first 3D printing project.
              </p>
              <div className="bg-primary-50 border border-primary-200 rounded-lg p-4 max-w-md mx-auto">
                <p className="text-primary-800 text-sm font-medium mb-2">Ready to get started?</p>
                <p className="text-primary-700 text-sm">
                  Click the <span className="inline-flex items-center px-2 py-1 bg-primary-600 text-white rounded-full text-xs">
                    <Plus className="h-3 w-3" />
                  </span> button in the bottom right corner to create your first print job!
                </p>
              </div>
            </>
          ) : (
            // Filtered but has jobs in other categories
            <>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No {statusFilter} jobs</h3>
              <p className="text-gray-500 mb-6 max-w-md mx-auto">
                You don't have any {statusFilter} jobs at the moment.
                {statusFilter === 'uncompleted' ? ' All your jobs are completed!' : ' Create a new job to get started.'}
              </p>
              {statusFilter === 'completed' && (
                <div className="bg-primary-50 border border-primary-200 rounded-lg p-4 max-w-md mx-auto">
                  <p className="text-primary-700 text-sm">
                    Use the <span className="inline-flex items-center px-2 py-1 bg-primary-600 text-white rounded-full text-xs">
                      <Plus className="h-3 w-3" />
                    </span> button to create a new job!
                  </p>
                </div>
              )}
            </>
          )}
        </div>
      )}
    </div>
  )
}

export default Jobs
