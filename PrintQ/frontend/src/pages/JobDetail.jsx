import { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom'
import { ArrowLeft, Edit, Trash2, Printer, Calendar, User, Package, Settings, ArrowUpNarrowWide, ArrowDownWideNarrow, X, FileText } from 'lucide-react'
import api from '../utils/api'
import JobForm from '../components/JobForm'
import PartEditForm from '../components/PartEditForm'

const JobDetail = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const [job, setJob] = useState(null)
  const [printers, setPrinters] = useState([])
  const [loading, setLoading] = useState(true)
  const [sortBy, setSortBy] = useState('none') // 'none', 'printer-asc', 'printer-desc'
  const [editingJob, setEditingJob] = useState(false)
  const [editingPart, setEditingPart] = useState(null)

  useEffect(() => {
    fetchJobDetail()
    fetchPrinters()
  }, [id])



  const fetchJobDetail = async () => {
    try {
      const jobData = await api.jobs.getById(id)
      setJob(jobData)
      setLoading(false)
    } catch (error) {
      console.error('Error fetching job detail:', error)
      setLoading(false)
    }
  }

  const fetchPrinters = async () => {
    try {
      const printersData = await api.printers.getAll()
      setPrinters(printersData)
    } catch (error) {
      console.error('Error fetching printers:', error)
    }
  }

  const handleAssignPrinter = async (partId, printerId) => {
    try {
      await api.parts.assign(job.id, partId, printerId)
      setJob(prev => ({
        ...prev,
        parts: prev.parts.map(part =>
          part.id === partId
            ? { ...part, printerId: printerId || null }
            : part
        )
      }))
    } catch (error) {
      console.error('Error assigning printer:', error)
      alert('Failed to assign printer. Please try again.')
    }
  }

  const handleEditJob = () => {
    setEditingJob(true)
  }

  const prepareJobDataForEdit = (jobData) => {
    return {
      ...jobData,
      dueDate: jobData.dueDate || ''
    }
  }

  const handleJobUpdated = (updatedJob) => {
    setJob(updatedJob)
    setEditingJob(false)
    // Refresh the job data to ensure we have the latest version
    fetchJobDetail()
  }

  const handleCancelEdit = () => {
    setEditingJob(false)
  }

  const handleEditPart = (part) => {
    setEditingPart(part)
  }

  const handlePartUpdated = (updatedPart) => {
    setJob(prev => ({
      ...prev,
      parts: prev.parts.map(part =>
        part.id === updatedPart.id ? updatedPart : part
      )
    }))
    setEditingPart(null)
  }

  const handleCancelPartEdit = () => {
    setEditingPart(null)
  }

  const handleDeleteJob = async () => {
    if (window.confirm('Are you sure you want to delete this job? This action cannot be undone.')) {
      try {
        await api.jobs.delete(job.id)
        navigate('/jobs?refresh=true')
      } catch (error) {
        console.error('Error deleting job:', error)
        alert('Failed to delete job. Please try again.')
      }
    }
  }



  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString()
  }

  const getPrinterName = (printerId) => {
    const printer = printers.find(p => p.id === printerId)
    return printer ? printer.name : 'Unassigned'
  }

  const getAvailablePrinters = () => {
    return printers
  }

  const getSortedParts = () => {
    if (!job || !job.parts) return []

    let sortedParts = [...job.parts]

    if (sortBy === 'printer-asc') {
      sortedParts.sort((a, b) => {
        const printerA = getPrinterName(a.printerId)
        const printerB = getPrinterName(b.printerId)

        // Unassigned parts go to the end
        if (printerA === 'Unassigned' && printerB !== 'Unassigned') return 1
        if (printerB === 'Unassigned' && printerA !== 'Unassigned') return -1
        if (printerA === 'Unassigned' && printerB === 'Unassigned') return 0

        return printerA.localeCompare(printerB)
      })
    } else if (sortBy === 'printer-desc') {
      sortedParts.sort((a, b) => {
        const printerA = getPrinterName(a.printerId)
        const printerB = getPrinterName(b.printerId)

        // Unassigned parts go to the end
        if (printerA === 'Unassigned' && printerB !== 'Unassigned') return 1
        if (printerB === 'Unassigned' && printerA !== 'Unassigned') return -1
        if (printerA === 'Unassigned' && printerB === 'Unassigned') return 0

        return printerB.localeCompare(printerA)
      })
    }

    return sortedParts
  }

  const handleSortChange = () => {
    if (sortBy === 'none') {
      setSortBy('printer-asc')
    } else if (sortBy === 'printer-asc') {
      setSortBy('printer-desc')
    } else {
      setSortBy('none')
    }
  }

  const getSortIcon = () => {
    if (sortBy === 'printer-asc') {
      return <ArrowUpNarrowWide className="h-4 w-4" />
    } else if (sortBy === 'printer-desc') {
      return <ArrowDownWideNarrow className="h-4 w-4" />
    }
    return null
  }

  const getSortLabel = () => {
    if (sortBy === 'printer-asc') {
      return 'Sorted by Printer (A-Z)'
    } else if (sortBy === 'printer-desc') {
      return 'Sorted by Printer (Z-A)'
    }
    return 'Sort by Printer'
  }

  const handlePrintView = () => {
    // Open print view in a new window/tab
    const printUrl = `/jobs/${id}/print`
    window.open(printUrl, '_blank')
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (!job) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900">Job not found</h3>
        <p className="text-gray-500">The requested job could not be found.</p>
        <button
          onClick={() => navigate('/jobs')}
          className="mt-4 btn-primary"
        >
          Back to Jobs
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/jobs')}
            className="text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="h-6 w-6" />
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{job.name}</h1>
            <div className="flex items-center space-x-4 text-gray-600">
              <div className="flex items-center space-x-1">
                <User className="h-4 w-4" />
                <span>{job.clientName}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div className="flex space-x-2">
          <button
            onClick={handlePrintView}
            className="btn-secondary flex items-center space-x-2"
          >
            <FileText className="h-4 w-4" />
            <span>Print View</span>
          </button>
          <button
            onClick={handleEditJob}
            className="btn-secondary flex items-center space-x-2"
          >
            <Edit className="h-4 w-4" />
            <span>Edit</span>
          </button>
          <button
            onClick={handleDeleteJob}
            className="btn-error flex items-center space-x-2"
          >
            <Trash2 className="h-4 w-4" />
            <span>Delete</span>
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Job Information */}
        <div className="lg:col-span-1 space-y-6">
          <div className="card p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Job Information</h2>
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-600">Created</p>
                  <p className="font-medium">{formatDate(job.createdAt)}</p>
                </div>
              </div>
              
              {job.dueDate && (
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-600">Due Date</p>
                    <p className="font-medium">{formatDate(job.dueDate)}</p>
                  </div>
                </div>
              )}
              
              <div className="flex items-center space-x-2">
                <Package className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-600">Total Parts</p>
                  <p className="font-medium">{job.parts.reduce((total, part) => total + part.quantity, 0)} pieces</p>
                </div>
              </div>
            </div>
            
            {job.description && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <h3 className="text-sm font-medium text-gray-700 mb-2">Description</h3>
                <p className="text-sm text-gray-600">{job.description}</p>
              </div>
            )}
          </div>
        </div>

        {/* Parts and Assignments */}
        <div className="lg:col-span-2">
          <div className="card p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">Parts & Printer Assignments</h2>
              <button
                onClick={handleSortChange}
                className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                {getSortIcon()}
                <span>{getSortLabel()}</span>
              </button>
            </div>
            <div className="space-y-4">
              {getSortedParts().map((part) => (
                <div key={part.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className="flex space-x-1">
                        {(part.colors || [part.color]).slice(0, 3).map((color, index) => (
                          <div
                            key={index}
                            className="w-4 h-4 rounded-full border border-gray-300"
                            style={{ backgroundColor: color }}
                            title={`Color ${index + 1}`}
                          />
                        ))}
                        {(part.colors || [part.color]).length > 3 && (
                          <div className="w-4 h-4 rounded-full border border-gray-300 bg-gray-100 flex items-center justify-center">
                            <span className="text-xs text-gray-600">+{(part.colors || [part.color]).length - 3}</span>
                          </div>
                        )}
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900">{part.name}</h3>
                        <p className="text-sm text-gray-600">
                          Quantity: {part.quantity} | Material: {part.material || 'Not specified'}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleEditPart(part)}
                        className="p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                        title="Edit part"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Printer Assignment */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Assigned Printer
                      </label>
                      <select
                        value={part.printerId || ''}
                        onChange={(e) => handleAssignPrinter(part.id, parseInt(e.target.value) || null)}
                        className="input-field"
                      >
                        <option value="">Unassigned</option>
                        {printers.map((printer) => (
                          <option
                            key={printer.id}
                            value={printer.id}
                          >
                            {printer.name}
                          </option>
                        ))}
                      </select>
                    </div>


                  </div>

                  {/* Printer Info */}
                  {part.printerId && (
                    <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <Printer className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-600">
                          Assigned to {getPrinterName(part.printerId)}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Edit Job Modal */}
      {editingJob && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-5xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">Edit Print Job</h2>
              <button
                onClick={handleCancelEdit}
                className="text-gray-400 hover:text-gray-600 transition-colors duration-200"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            <div className="p-6">
              <JobForm
                initialData={prepareJobDataForEdit(job)}
                onSubmit={handleJobUpdated}
                onCancel={handleCancelEdit}
              />
            </div>
          </div>
        </div>
      )}

      {/* Edit Part Modal */}
      {editingPart && (
        <PartEditForm
          part={editingPart}
          jobId={job.id}
          onSubmit={handlePartUpdated}
          onCancel={handleCancelPartEdit}
        />
      )}
    </div>
  )
}

export default JobDetail
