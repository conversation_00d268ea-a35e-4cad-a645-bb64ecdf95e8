import { RefreshCw } from 'lucide-react'

const RefreshIndicator = ({ isRefreshing, lastRefresh }) => {
  if (!isRefreshing && !lastRefresh) return null

  return (
    <div className="flex items-center space-x-2 text-sm text-gray-500">
      {isRefreshing ? (
        <>
          <RefreshCw className="h-4 w-4 animate-spin" />
          <span>Refreshing data...</span>
        </>
      ) : (
        <>
          <RefreshCw className="h-4 w-4" />
          <span>Last updated: {new Date(lastRefresh).toLocaleTimeString()}</span>
        </>
      )}
    </div>
  )
}

export default RefreshIndicator
