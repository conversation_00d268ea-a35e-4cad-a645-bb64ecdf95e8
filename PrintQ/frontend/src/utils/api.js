// Use relative URL for production, localhost for development
const API_BASE_URL = import.meta.env.PROD ? '/api' : 'http://localhost:3001/api'

// Generate cache-busting parameter
function getCacheBuster() {
  return Date.now().toString()
}

// Add cache-busting query parameter to URL
function addCache<PERSON>uster(url) {
  const separator = url.includes('?') ? '&' : '?'
  return `${url}${separator}_cb=${getCacheBuster()}`
}

// Generic API request function
async function apiRequest(endpoint, options = {}) {
  let url = `${API_BASE_URL}${endpoint}`

  // Add cache busting for GET requests
  if (!options.method || options.method === 'GET') {
    url = addCacheBuster(url)
  }

  const config = {
    headers: {
      'Content-Type': 'application/json',
      // Add cache-busting headers
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
      ...options.headers,
    },
    ...options,
  }

  if (config.body && typeof config.body === 'object') {
    config.body = JSON.stringify(config.body)
  }

  try {
    const response = await fetch(url, config)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`)
    }

    // Handle 204 No Content responses
    if (response.status === 204) {
      return null
    }

    return await response.json()
  } catch (error) {
    console.error(`API request failed: ${endpoint}`, error)
    throw error
  }
}

// Jobs API
export const jobsApi = {
  // Get all jobs
  getAll: () => apiRequest('/jobs'),
  
  // Get job by ID
  getById: (id) => apiRequest(`/jobs/${id}`),
  
  // Create new job
  create: (jobData) => apiRequest('/jobs', {
    method: 'POST',
    body: jobData,
  }),
  
  // Update job
  update: (id, updates) => apiRequest(`/jobs/${id}`, {
    method: 'PUT',
    body: updates,
  }),
  
  // Delete job
  delete: (id) => apiRequest(`/jobs/${id}`, {
    method: 'DELETE',
  }),
  
  // Get jobs by status
  getByStatus: (status) => apiRequest(`/jobs/status/${status}`),
}

// Printers API
export const printersApi = {
  // Get all printers
  getAll: () => apiRequest('/printers'),

  // Get printer by ID
  getById: (id) => apiRequest(`/printers/${id}`),

  // Create new printer
  create: (printerData) => apiRequest('/printers', {
    method: 'POST',
    body: printerData,
  }),

  // Update printer
  update: (id, updates) => apiRequest(`/printers/${id}`, {
    method: 'PUT',
    body: updates,
  }),

  // Delete printer
  delete: (id) => apiRequest(`/printers/${id}`, {
    method: 'DELETE',
  }),



  // Get active prints for a specific printer
  getActivePrints: (id) => apiRequest(`/printers/${id}/active-prints`),
}

// Parts API
export const partsApi = {
  // Assign part to printer
  assign: (jobId, partId, printerId) => apiRequest(`/parts/${jobId}/${partId}/assign`, {
    method: 'PUT',
    body: { printerId },
  }),

  // Update part status
  updateStatus: (jobId, partId, status, progress) => apiRequest(`/parts/${jobId}/${partId}/status`, {
    method: 'PUT',
    body: { status, progress },
  }),

  // Update part progress
  updateProgress: (jobId, partId, progress) => apiRequest(`/parts/${jobId}/${partId}/progress`, {
    method: 'PUT',
    body: { progress },
  }),

  // Update part details
  update: (jobId, partId, partData) => apiRequest(`/parts/${jobId}/${partId}`, {
    method: 'PUT',
    body: partData,
  }),
}

// Settings API
export const settingsApi = {
  // Get settings
  get: () => apiRequest('/settings'),

  // Update settings
  update: (settingsData) => apiRequest('/settings', {
    method: 'PUT',
    body: settingsData,
  }),
}

// Health check
export const healthCheck = () => apiRequest('/health')

// Force refresh all data by making fresh API calls
export const refreshAllData = async () => {
  try {
    const [jobs, printers, settings] = await Promise.all([
      jobsApi.getAll(),
      printersApi.getAll(),
      settingsApi.get()
    ])
    return { jobs, printers, settings }
  } catch (error) {
    console.error('Error refreshing all data:', error)
    throw error
  }
}

export default {
  jobs: jobsApi,
  printers: printersApi,
  parts: partsApi,
  settings: settingsApi,
  healthCheck,
  refreshAllData,
}
